import React from 'react';
import styled from 'styled-components';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = React.memo(({
  size = 'md',
  className = ''
}) => {
  const sizeMultiplier = {
    sm: 0.6,
    md: 1,
    lg: 1.5
  };

  const scale = sizeMultiplier[size];

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <StyledWrapper scale={scale}>
        <div className="spinner">
          <div className="spinnerin" />
        </div>
      </StyledWrapper>
    </div>
  );
});

const StyledWrapper = styled.div<{ scale: number }>`
  transform: scale(${props => props.scale});

  .spinner {
    width: 3em;
    height: 3em;
    cursor: not-allowed;
    border-radius: 50%;
    border: 2px solid #444;
    box-shadow: -10px -10px 10px #6359f8, 0px -10px 10px 0px #9c32e2, 10px -10px 10px #f36896, 10px 0 10px #ff0b0b, 10px 10px 10px 0px#ff5500, 0 10px 10px 0px #ff9500, -10px 10px 10px 0px #ffb700;
    animation: rot55 0.7s linear infinite;
  }

  .spinnerin {
    border: 2px solid #444;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  @keyframes rot55 {
    to {
      transform: rotate(360deg);
    }
  }
`;

LoadingSpinner.displayName = 'LoadingSpinner';

export default LoadingSpinner;
