// Utility functions for user management

/**
 * Generate a cryptographically secure random string
 * @param length - Length of the random string
 * @returns Random string using base36 encoding
 */
const generateSecureRandomString = (length: number): string => {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    // Use crypto API for better randomness
    const array = new Uint8Array(length)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(36)).join('').slice(0, length)
  } else {
    // Fallback to Math.random with multiple iterations for better entropy
    let result = ''
    for (let i = 0; i < length; i++) {
      result += Math.random().toString(36).charAt(2)
    }
    return result.padEnd(length, '0')
  }
}

/**
 * Generate browser fingerprint components for additional uniqueness
 * @returns String containing browser/device specific information hash
 */
const getBrowserFingerprint = (): string => {
  const components = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    screen.colorDepth,
    new Date().getTimezoneOffset(),
    navigator.hardwareConcurrency || 0,
    // Safe access to deviceMemory with fallback
    (navigator as any).deviceMemory || 0
  ]

  // Create a simple hash from components
  let hash = 0
  const str = components.join('|')
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36)
}

/**
 * Generate a unique user ID for anonymous users
 * This creates a highly unique string for session identification using:
 * - Cryptographically secure random values
 * - High-precision timestamp
 * - Browser fingerprint
 * - Performance timing for additional entropy
 */
export const generateAnonymousUserId = (): string => {
  const timestamp = Date.now().toString(36)
  const microTime = performance.now().toString(36).replace('.', '')
  const randomPart1 = generateSecureRandomString(8)
  const randomPart2 = generateSecureRandomString(6)
  const fingerprint = getBrowserFingerprint()
  const sessionRandom = Math.random().toString(36).substring(2, 6)

  return `user_${randomPart1}_${timestamp}_${microTime}_${fingerprint}_${randomPart2}_${sessionRandom}`
}

/**
 * Get user ID from localStorage or generate a new one
 * This ensures consistent user identification across sessions
 */
export const getOrCreateUserId = (): string => {
  const STORAGE_KEY = 'playground_user_id'
  
  try {
    // Try to get existing user ID from localStorage
    const existingUserId = localStorage.getItem(STORAGE_KEY)
    
    if (existingUserId) {
      return existingUserId
    }
    
    // Generate new user ID if none exists
    const newUserId = generateAnonymousUserId()
    localStorage.setItem(STORAGE_KEY, newUserId)
    
    return newUserId
  } catch (error) {
    // Fallback if localStorage is not available
    console.warn('localStorage not available, using session-only user ID:', error)
    return generateAnonymousUserId()
  }
}

/**
 * Clear user ID from localStorage
 * Useful for logout or reset functionality
 */
export const clearUserId = (): void => {
  const STORAGE_KEY = 'playground_user_id'
  
  try {
    localStorage.removeItem(STORAGE_KEY)
  } catch (error) {
    console.warn('Failed to clear user ID from localStorage:', error)
  }
}

/**
 * Validate user ID format
 * Ensures the user ID follows expected format
 */
export const isValidUserId = (userId: string | null): boolean => {
  if (!userId) return false

  // Check if it's our new generated format (more complex with multiple parts)
  const newAnonymousPattern = /^user_[a-z0-9]{8}_[a-z0-9]+_[a-z0-9]+_[a-z0-9]+_[a-z0-9]{6}_[a-z0-9]{4}$/
  // Check if it's our old generated format (for backward compatibility)
  const oldAnonymousPattern = /^user_[a-z0-9]{9}_[a-z0-9]+$/
  // Check if it's a valid custom format
  const customPattern = /^[a-zA-Z0-9_-]{3,100}$/

  return newAnonymousPattern.test(userId) || oldAnonymousPattern.test(userId) || customPattern.test(userId)
}
