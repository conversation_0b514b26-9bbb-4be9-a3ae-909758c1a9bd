export const APIRoutes = {
  GetPlaygroundAgents: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/playground/agents`,
  // Updated to use new chat stream endpoint
  ChatStream: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/chat/stream`,
  PlaygroundStatus: (PlaygroundApiUrl: string) =>
    `${PlaygroundApiUrl}/v1/playground/status`,
  // Updated to use new chat sessions endpoint with user_id as path parameter
  GetChatSessions: (PlaygroundApiUrl: string, userId: string) =>
    `${PlaygroundApiUrl}/v1/chat/sessions/${userId}`,
  // Updated to use new delete session endpoint
  DeleteChatSession: (PlaygroundApiUrl: string, sessionId: string) =>
    `${PlaygroundApiUrl}/v1/chat/sessions/${sessionId}`,

  // Updated to use new chat history endpoint with session_id as path parameter
  GetChatHistory: (PlaygroundApiUrl: string, sessionId: string) =>
    `${PlaygroundApiUrl}/v1/chat/history/${sessionId}`
}
