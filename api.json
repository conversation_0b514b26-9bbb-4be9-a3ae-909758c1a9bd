{"openapi": "3.1.0", "info": {"title": "FPT University Agent API", "description": "API for FPT University Agent with Intent Detection and Agno Integration", "version": "1.0.0"}, "paths": {"/v1/health": {"get": {"tags": ["Health"], "summary": "Health Check", "description": "Health check endpoint\n\nReturns:\n    Health status of the API and its components", "operationId": "health_check_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/chat/send": {"post": {"tags": ["Cha<PERSON>"], "summary": "Send Message", "description": "Send a message to the FPT University Agent", "operationId": "send_message_v1_chat_send_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/stream": {"post": {"tags": ["Cha<PERSON>"], "summary": "Stream Message", "description": "Stream a message using Agno's built-in streaming with tool calls", "operationId": "stream_message_v1_chat_stream_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/memory/{user_id}": {"get": {"tags": ["Cha<PERSON>"], "summary": "Get User Memories", "description": "Get user memories from the agent's memory system using Agno standard method", "operationId": "get_user_memories_v1_chat_memory__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Cha<PERSON>"], "summary": "Clear User Memories", "description": "Clear all memories for a specific user using standard Agno method", "operationId": "clear_user_memories_v1_chat_memory__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/sessions/{user_id}": {"get": {"tags": ["Cha<PERSON>"], "summary": "Get User Sessions", "description": "Get all sessions for a specific user, including their names.", "operationId": "get_user_sessions_v1_chat_sessions__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/history/{session_id}": {"get": {"tags": ["Cha<PERSON>"], "summary": "Get Session History", "description": "Get conversation history for a specific session using Agno standard method", "operationId": "get_session_history_v1_chat_history__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/sessions/{session_id}": {"delete": {"tags": ["Cha<PERSON>"], "summary": "Delete Session", "description": "Delete a specific session and all its conversation history", "operationId": "delete_session_v1_chat_sessions__session_id__delete", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/api/knowledge/upload": {"post": {"tags": ["Knowledge"], "summary": "Upload Document Optimized", "description": "Optimized document upload using Agno built-in functions\n\nKey improvements:\n- Uses Agno's native file format support\n- Leverages built-in async processing\n- Eliminates custom metadata extraction\n- Uses Agno's upsert functionality for duplicates", "operationId": "upload_document_optimized_v1_api_knowledge_upload_post", "parameters": [{"name": "overwrite", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Overwrite"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_document_optimized_v1_api_knowledge_upload_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/api/knowledge/documents": {"get": {"tags": ["Knowledge"], "summary": "List Documents Optimized", "description": "List documents using simplified approach\nAgno handles the complex knowledge base queries", "operationId": "list_documents_optimized_v1_api_knowledge_documents_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DocumentInfo"}, "type": "array", "title": "Response List Documents Optimized V1 Api Knowledge Documents Get"}}}}}}}, "/v1/api/knowledge/documents/{filename}": {"delete": {"tags": ["Knowledge"], "summary": "Delete Document Optimized", "description": "Delete document using Agno's built-in methods\nMuch simpler than manual vector DB operations", "operationId": "delete_document_optimized_v1_api_knowledge_documents__filename__delete", "parameters": [{"name": "filename", "in": "path", "required": true, "schema": {"type": "string", "title": "Filename"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/api/knowledge/status": {"get": {"tags": ["Knowledge"], "summary": "Get Knowledge Status Optimized", "description": "Get knowledge base status using Agno's built-in methods\nSimplified compared to custom implementation", "operationId": "get_knowledge_status_optimized_v1_api_knowledge_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/api/knowledge/reload": {"post": {"tags": ["Knowledge"], "summary": "Reload Knowledge Base", "description": "Reload entire knowledge base using Agno's built-in aload()\nMuch simpler than custom reload logic", "operationId": "reload_knowledge_base_v1_api_knowledge_reload_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/playground/status": {"get": {"tags": ["Playground"], "summary": "Playground Status", "operationId": "playground_status_v1_playground_status_get", "parameters": [{"name": "app_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents": {"get": {"tags": ["Playground"], "summary": "Get Agents", "operationId": "get_agents_v1_playground_agents_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/AgentGetResponse"}, "type": "array", "title": "Response Get Agents V1 Playground Agents Get"}}}}}}}, "/v1/playground/agents/{agent_id}/runs": {"post": {"tags": ["Playground"], "summary": "Create Agent Run", "operationId": "create_agent_run_v1_playground_agents__agent_id__runs_post", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_create_agent_run_v1_playground_agents__agent_id__runs_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents/{agent_id}/runs/{run_id}/continue": {"post": {"tags": ["Playground"], "summary": "Continue Agent Run", "operationId": "continue_agent_run_v1_playground_agents__agent_id__runs__run_id__continue_post", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "run_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Run Id"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_continue_agent_run_v1_playground_agents__agent_id__runs__run_id__continue_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents/{agent_id}/sessions": {"get": {"tags": ["Playground"], "summary": "Get All Agent Sessions", "operationId": "get_all_agent_sessions_v1_playground_agents__agent_id__sessions_get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents/{agent_id}/sessions/{session_id}": {"get": {"tags": ["Playground"], "summary": "Get Agent Session", "operationId": "get_agent_session_v1_playground_agents__agent_id__sessions__session_id__get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Playground"], "summary": "Delete Agent Session", "operationId": "delete_agent_session_v1_playground_agents__agent_id__sessions__session_id__delete", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents/{agent_id}/sessions/{session_id}/rename": {"post": {"tags": ["Playground"], "summary": "Rename Agent Session", "operationId": "rename_agent_session_v1_playground_agents__agent_id__sessions__session_id__rename_post", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRenameRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/agents/{agent_id}/memories": {"get": {"tags": ["Playground"], "summary": "Get Agent Memories", "operationId": "get_agent_memories_v1_playground_agents__agent_id__memories_get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Id"}}, {"name": "user_id", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/workflows": {"get": {"tags": ["Playground"], "summary": "Get Workflows", "operationId": "get_workflows_v1_playground_workflows_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/WorkflowsGetResponse"}, "type": "array", "title": "Response Get Workflows V1 Playground Workflows Get"}}}}}}}, "/v1/playground/workflows/{workflow_id}": {"get": {"tags": ["Playground"], "summary": "Get Workflow", "operationId": "get_workflow_v1_playground_workflows__workflow_id__get", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowGetResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/workflows/{workflow_id}/runs": {"post": {"tags": ["Playground"], "summary": "Create Workflow Run", "operationId": "create_workflow_run_v1_playground_workflows__workflow_id__runs_post", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowRunRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/workflows/{workflow_id}/sessions": {"get": {"tags": ["Playground"], "summary": "Get All Workflow Sessions", "operationId": "get_all_workflow_sessions_v1_playground_workflows__workflow_id__sessions_get", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/workflows/{workflow_id}/sessions/{session_id}": {"get": {"tags": ["Playground"], "summary": "Get Workflow Session", "operationId": "get_workflow_session_v1_playground_workflows__workflow_id__sessions__session_id__get", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Playground"], "summary": "Delete Workflow Session", "operationId": "delete_workflow_session_v1_playground_workflows__workflow_id__sessions__session_id__delete", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/workflows/{workflow_id}/sessions/{session_id}/rename": {"post": {"tags": ["Playground"], "summary": "Rename Workflow Session", "operationId": "rename_workflow_session_v1_playground_workflows__workflow_id__sessions__session_id__rename_post", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Workflow Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowRenameRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/teams": {"get": {"tags": ["Playground"], "summary": "Get Teams", "operationId": "get_teams_v1_playground_teams_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/playground/teams/{team_id}": {"get": {"tags": ["Playground"], "summary": "Get Team", "operationId": "get_team_v1_playground_teams__team_id__get", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/teams/{team_id}/runs": {"post": {"tags": ["Playground"], "summary": "Create Team Run", "operationId": "create_team_run_v1_playground_teams__team_id__runs_post", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_create_team_run_v1_playground_teams__team_id__runs_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/teams/{team_id}/sessions": {"get": {"tags": ["Playground"], "summary": "Get All Team Sessions", "operationId": "get_all_team_sessions_v1_playground_teams__team_id__sessions_get", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TeamSessionResponse"}, "title": "Response Get All Team Sessions V1 Playground Teams  Team Id  Sessions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/teams/{team_id}/sessions/{session_id}": {"get": {"tags": ["Playground"], "summary": "Get Team Session", "operationId": "get_team_session_v1_playground_teams__team_id__sessions__session_id__get", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Playground"], "summary": "Delete Team Session", "operationId": "delete_team_session_v1_playground_teams__team_id__sessions__session_id__delete", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/teams/{team_id}/sessions/{session_id}/rename": {"post": {"tags": ["Playground"], "summary": "Rename Team Session", "operationId": "rename_team_session_v1_playground_teams__team_id__sessions__session_id__rename_post", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}, {"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamRenameRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/playground/team/{team_id}/memories": {"get": {"tags": ["Playground"], "summary": "Get Team Memories", "operationId": "get_team_memories_v1_playground_team__team_id__memories_get", "parameters": [{"name": "team_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Team Id"}}, {"name": "user_id", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AgentGetResponse": {"properties": {"agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "model": {"anyOf": [{"$ref": "#/components/schemas/AgentModel"}, {"type": "null"}]}, "add_context": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Add Context"}, "tools": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Tools"}, "memory": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Memory"}, "storage": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Storage"}, "knowledge": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Knowledge"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "instructions": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Instructions"}}, "type": "object", "title": "AgentGetResponse"}, "AgentModel": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Provider"}}, "type": "object", "title": "AgentModel"}, "AgentRenameRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "user_id": {"type": "string", "title": "User Id"}}, "type": "object", "required": ["name", "user_id"], "title": "AgentRenameRequest"}, "Body_continue_agent_run_v1_playground_agents__agent_id__runs__run_id__continue_post": {"properties": {"tools": {"type": "string", "title": "Tools"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "stream": {"type": "boolean", "title": "Stream", "default": true}}, "type": "object", "required": ["tools"], "title": "Body_continue_agent_run_v1_playground_agents__agent_id__runs__run_id__continue_post"}, "Body_create_agent_run_v1_playground_agents__agent_id__runs_post": {"properties": {"message": {"type": "string", "title": "Message"}, "stream": {"type": "boolean", "title": "Stream", "default": true}, "monitor": {"type": "boolean", "title": "Monitor", "default": false}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "files": {"anyOf": [{"items": {"type": "string", "format": "binary"}, "type": "array"}, {"type": "null"}], "title": "Files"}}, "type": "object", "required": ["message"], "title": "Body_create_agent_run_v1_playground_agents__agent_id__runs_post"}, "Body_create_team_run_v1_playground_teams__team_id__runs_post": {"properties": {"message": {"type": "string", "title": "Message"}, "stream": {"type": "boolean", "title": "Stream", "default": true}, "monitor": {"type": "boolean", "title": "Monitor", "default": true}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "files": {"anyOf": [{"items": {"type": "string", "format": "binary"}, "type": "array"}, {"type": "null"}], "title": "Files"}}, "type": "object", "required": ["message"], "title": "Body_create_team_run_v1_playground_teams__team_id__runs_post"}, "Body_upload_document_optimized_v1_api_knowledge_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_document_optimized_v1_api_knowledge_upload_post"}, "ChatRequest": {"properties": {"message": {"type": "string", "maxLength": 4000, "minLength": 1, "title": "Message", "description": "User message"}, "user_id": {"type": "string", "maxLength": 100, "minLength": 1, "title": "User Id", "description": "Unique user identifier"}, "session_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Session Id", "description": "Session identifier for conversation continuity"}}, "type": "object", "required": ["message", "user_id"], "title": "ChatRequest", "description": "Chat request with validation"}, "DocumentInfo": {"properties": {"filename": {"type": "string", "title": "Filename"}, "size": {"type": "integer", "title": "Size"}, "modified": {"type": "string", "title": "Modified"}, "exists": {"type": "boolean", "title": "Exists"}}, "type": "object", "required": ["filename", "size", "modified", "exists"], "title": "DocumentInfo", "description": "Simplified document info using Agno's built-in metadata"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TeamRenameRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "user_id": {"type": "string", "title": "User Id"}}, "type": "object", "required": ["name", "user_id"], "title": "TeamRenameRequest"}, "TeamSessionResponse": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "session_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Name"}, "created_at": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created At"}}, "type": "object", "title": "TeamSessionResponse"}, "UploadResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "file_path": {"type": "string", "title": "File Path"}, "file_size": {"type": "integer", "title": "File Size"}, "processing_status": {"type": "string", "title": "Processing Status"}, "agno_optimized": {"type": "boolean", "title": "Agno Optimized", "default": true}}, "type": "object", "required": ["message", "file_path", "file_size", "processing_status"], "title": "UploadResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WorkflowGetResponse": {"properties": {"workflow_id": {"type": "string", "title": "Workflow Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "parameters": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Parameters"}, "storage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Storage"}}, "type": "object", "required": ["workflow_id"], "title": "WorkflowGetResponse"}, "WorkflowRenameRequest": {"properties": {"name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["name"], "title": "WorkflowRenameRequest"}, "WorkflowRunRequest": {"properties": {"input": {"additionalProperties": true, "type": "object", "title": "Input"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "stream": {"type": "boolean", "title": "Stream", "default": true}}, "type": "object", "required": ["input"], "title": "WorkflowRunRequest"}, "WorkflowsGetResponse": {"properties": {"workflow_id": {"type": "string", "title": "Workflow Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["workflow_id", "name"], "title": "WorkflowsGetResponse"}}}}