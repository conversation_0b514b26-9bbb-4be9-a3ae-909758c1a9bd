import { useCallback } from 'react'
import { toast } from 'sonner'
import { deleteChatSessionAPI } from '@/services/api/playground'
import { usePlaygroundStore } from '@/store'
import { useQueryState } from 'nuqs'

/**
 * Hook for managing session operations like deletion
 */
const useSessionManager = () => {
  const selectedEndpoint = usePlaygroundStore((state) => state.selectedEndpoint)
  const userId = usePlaygroundStore((state) => state.userId)
  const setSessionsData = usePlaygroundStore((state) => state.setSessionsData)
  const setMessages = usePlaygroundStore((state) => state.setMessages)
  const [currentSessionId, setCurrentSessionId] = useQueryState('session')

  /**
   * Delete a session by ID
   * @param sessionId - The session ID to delete
   * @returns Promise<boolean> - Success status
   */
  const deleteSession = useCallback(
    async (sessionId: string): Promise<boolean> => {
      if (!sessionId || !selectedEndpoint) {
        toast.error('Missing required parameters for session deletion')
        return false
      }

      try {
        const response = await deleteChatSessionAPI(
          selectedEndpoint,
          sessionId,
          userId
        )

        if (response.ok) {
          // Check if we're deleting the currently active session
          const isDeletingCurrentSession = currentSessionId === sessionId

          // Remove the deleted session from local state
          setSessionsData((prevSessions) =>
            prevSessions?.filter(session => session.id !== sessionId) ?? null
          )

          // If deleting current session, redirect to welcome screen
          if (isDeletingCurrentSession) {
            setCurrentSessionId(null) // Clear session from URL
            setMessages([]) // Clear messages from store
            console.log('Redirected to welcome screen after deleting current session')
          }

          toast.success('Session deleted successfully')
          return true
        } else {
          throw new Error(`Failed to delete session: ${response.statusText}`)
        }
      } catch (error) {
        console.error('Error deleting session:', error)
        toast.error('Failed to delete session')
        return false
      }
    },
    [selectedEndpoint, userId, setSessionsData]
  )

  /**
   * Delete multiple sessions
   * @param sessionIds - Array of session IDs to delete
   * @returns Promise<number> - Number of successfully deleted sessions
   */
  const deleteSessions = useCallback(
    async (sessionIds: string[]): Promise<number> => {
      if (!sessionIds.length || !selectedEndpoint) {
        toast.error('Missing required parameters for bulk session deletion')
        return 0
      }

      let successCount = 0
      const promises = sessionIds.map(async (sessionId) => {
        try {
          const response = await deleteChatSessionAPI(
            selectedEndpoint,
            sessionId,
            userId
          )
          
          if (response.ok) {
            successCount++
            return sessionId
          }
          return null
        } catch (error) {
          console.error(`Error deleting session ${sessionId}:`, error)
          return null
        }
      })

      const results = await Promise.all(promises)
      const deletedSessionIds = results.filter(Boolean) as string[]

      if (deletedSessionIds.length > 0) {
        // Check if we're deleting the currently active session
        const isDeletingCurrentSession = currentSessionId && deletedSessionIds.includes(currentSessionId)

        // Remove deleted sessions from local state
        setSessionsData((prevSessions) =>
          prevSessions?.filter(session =>
            !deletedSessionIds.includes(session.id)
          ) ?? null
        )

        // If deleting current session, redirect to welcome screen
        if (isDeletingCurrentSession) {
          setCurrentSessionId(null) // Clear session from URL
          setMessages([]) // Clear messages from store
          console.log('Redirected to welcome screen after bulk deleting current session')
        }
      }

      if (successCount === sessionIds.length) {
        toast.success(`Successfully deleted ${successCount} sessions`)
      } else if (successCount > 0) {
        toast.warning(`Deleted ${successCount} out of ${sessionIds.length} sessions`)
      } else {
        toast.error('Failed to delete any sessions')
      }

      return successCount
    },
    [selectedEndpoint, userId, setSessionsData]
  )

  return {
    deleteSession,
    deleteSessions
  }
}

export default useSessionManager
