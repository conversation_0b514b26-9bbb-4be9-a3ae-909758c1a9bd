@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS variables for better performance */
:root {
  --primary-blue: #3b82f6;
  --primary-blue-600: #2563eb;
  --primary-blue-700: #1d4ed8;
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-600) 50%, #8b5cf6 100%);
  --shadow-primary: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

/* Optimize animations for better performance */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize font loading */
@layer base {
  html {
    font-display: swap;
  }
}

/* Custom utility classes for common patterns */
@layer utilities {
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: rgba(209, 213, 219, 0.5) transparent;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgba(209, 213, 219, 0.5);
  }

  .scrollbar-thumb-gray-600 {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
  }

  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Prevent body scroll when mobile sidebar is open */
  .mobile-sidebar-open {
    overflow: hidden;
  }

  @media (min-width: 1024px) {
    .mobile-sidebar-open {
      overflow: auto;
    }
  }
}
