import { useCallback } from 'react'
import {
  getAllPlaygroundSessionsAPI,
  getChatHistoryAPI
} from '@/services/api/playground'
import { usePlaygroundStore } from '@/store'
import { toast } from 'sonner'
import {
  PlaygroundChatMessage
} from '@/types/playground'

const useSessionLoader = () => {
  const setMessages = usePlaygroundStore((state) => state.setMessages)
  const selectedEndpoint = usePlaygroundStore((state) => state.selectedEndpoint)
  const userId = usePlaygroundStore((state) => state.userId)
  const setIsSessionsLoading = usePlaygroundStore(
    (state) => state.setIsSessionsLoading
  )
  const setSessionsData = usePlaygroundStore((state) => state.setSessionsData)

  const getSessions = useCallback(
    async (agentId: string) => {
      if (!agentId || !selectedEndpoint) return
      try {
        setIsSessionsLoading(true)
        const sessions = await getAllPlaygroundSessionsAPI(
          selectedEndpoint,
          agentId,
          userId
        )
        setSessionsData(sessions)
      } catch {
        toast.error('Error loading sessions')
      } finally {
        setIsSessionsLoading(false)
      }
    },
    [selectedEndpoint, userId, setSessionsData, setIsSessionsLoading]
  )

  const getSession = useCallback(
    async (sessionId: string, _agentId: string) => {
      if (!sessionId || !selectedEndpoint) {
        return null
      }

      try {
        const response = await getChatHistoryAPI(
          selectedEndpoint,
          sessionId,
          userId
        )

        if (!response || !response.history) {
          return null
        }

        // Convert ChatHistoryMessage to PlaygroundChatMessage
        // Filter out system messages to prevent UI confusion
        const messages: PlaygroundChatMessage[] = response.history
          .filter((msg) => msg.role !== 'system')
          .map((msg) => ({
            role: msg.role === 'assistant' ? 'agent' : msg.role,
            content: msg.content,
            created_at: msg.created_at
          }))

        setMessages(messages)
        return messages
      } catch (error) {
        console.error('Error fetching session history:', error)
        return null
      }
    },
    [selectedEndpoint, userId, setMessages]
  )

  return { getSession, getSessions }
}

export { useSessionLoader }
export default useSessionLoader
