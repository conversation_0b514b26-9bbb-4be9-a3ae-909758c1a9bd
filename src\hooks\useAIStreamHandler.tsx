import { useCallback } from 'react'

import { APIRoutes } from '@/services/api/routes'

import useChatActions from '@/hooks/useChatActions'
import { usePlaygroundStore } from '@/store'
import {
  RunEvent,
  type RunResponse
} from '@/types/playground'
import { constructEndpointUrl } from '@/lib/utils'
import useAIResponseStream from './useAIResponseStream'

import { useQueryState } from 'nuqs'
import { getJsonMarkdown } from '@/lib/utils'

/**
 * useAIChatStreamHandler is responsible for making API calls and handling the stream response.
 * For now, it only streams message content and updates the messages state.
 */
const useAIChatStreamHandler = () => {
  const setMessages = usePlaygroundStore((state) => state.setMessages)
  const { addMessage, focusChatInput } = useChatActions()
  const [agentId] = useQueryState('agent')
  const [sessionId, setSessionId] = useQueryState('session')
  const selectedEndpoint = usePlaygroundStore((state) => state.selectedEndpoint)
  const userId = usePlaygroundStore((state) => state.userId)
  const setStreamingErrorMessage = usePlaygroundStore(
    (state) => state.setStreamingErrorMessage
  )
  const setIsStreaming = usePlaygroundStore((state) => state.setIsStreaming)
  const setSessionsData = usePlaygroundStore((state) => state.setSessionsData)
  const hasStorage = usePlaygroundStore((state) => state.hasStorage)
  const { streamResponse } = useAIResponseStream()

  const updateMessagesWithErrorState = useCallback(() => {
    setMessages((prevMessages) => {
      const newMessages = [...prevMessages]
      let lastMessage = newMessages[newMessages.length - 1]

      // Create agent message if it doesn't exist yet
      if (!lastMessage || lastMessage.role !== 'agent') {
        const agentMessage = {
          role: 'agent' as const,
          content: '',
          streamingError: true,
          created_at: Math.floor(Date.now() / 1000)
        }
        newMessages.push(agentMessage)
      } else {
        lastMessage.streamingError = true
      }
      return newMessages
    })
  }, [setMessages])



  const handleStreamResponse = useCallback(
    async (input: string | FormData) => {
      setIsStreaming(true)

      // Extract message from input
      const message = input instanceof FormData ? (input.get('message') as string) : input

      setMessages((prevMessages) => {
        if (prevMessages.length >= 2) {
          const lastMessage = prevMessages[prevMessages.length - 1]
          const secondLastMessage = prevMessages[prevMessages.length - 2]
          if (
            lastMessage.role === 'agent' &&
            lastMessage.streamingError &&
            secondLastMessage.role === 'user'
          ) {
            return prevMessages.slice(0, -2)
          }
        }
        return prevMessages
      })

      addMessage({
        role: 'user',
        content: message,
        created_at: Math.floor(Date.now() / 1000)
      })

      // Don't add empty agent message immediately
      // We'll add it when we get the first content chunk

      let lastContent = ''
      let newSessionId = sessionId
      try {
        const endpointUrl = constructEndpointUrl(selectedEndpoint)

        // Use new chat stream endpoint
        const chatStreamUrl = APIRoutes.ChatStream(endpointUrl)

        // Generate session_id if not exists
        const currentSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

        // Set session_id in URL if it was generated
        if (!sessionId && currentSessionId) {
          setSessionId(currentSessionId)
          newSessionId = currentSessionId
        }

        // Create request body for new API format
        const requestBody = {
          message: message,
          user_id: userId || '',
          session_id: currentSessionId
        }

        await streamResponse({
          apiUrl: chatStreamUrl,
          requestBody: requestBody,
          onChunk: (chunk: RunResponse) => {
            if (
              chunk.event === RunEvent.RunStarted ||
              chunk.event === RunEvent.ReasoningStarted
            ) {
              newSessionId = chunk.session_id as string
              setSessionId(chunk.session_id as string)
              if (
                hasStorage &&
                (!sessionId || sessionId !== chunk.session_id) &&
                chunk.session_id
              ) {
                const sessionData = {
                  id: chunk.session_id as string,
                  name: message
                }
                setSessionsData((prevSessionsData) => {
                  const sessionExists = prevSessionsData?.some(
                    (session) => session.id === chunk.session_id
                  )
                  if (sessionExists) {
                    return prevSessionsData
                  }
                  return [sessionData, ...(prevSessionsData ?? [])]
                })
              }

            } else if (
              chunk.event === RunEvent.RunResponse ||
              chunk.event === RunEvent.RunResponseContent
            ) {
              setMessages((prevMessages) => {
                const newMessages = [...prevMessages]
                let lastMessage = newMessages[newMessages.length - 1]

                // Create agent message if it doesn't exist yet
                if (!lastMessage || lastMessage.role !== 'agent') {
                  const agentMessage = {
                    role: 'agent' as const,
                    content: '',
                    streamingError: false,
                    created_at: Math.floor(Date.now() / 1000)
                  }
                  newMessages.push(agentMessage)
                  lastMessage = agentMessage
                }

                if (typeof chunk.content === 'string' && chunk.content !== null) {
                  // With new API, content is streamed incrementally, not accumulated
                  lastMessage.content += chunk.content
                  lastContent += chunk.content


                  if (chunk.extra_data?.reasoning_steps) {
                    lastMessage.extra_data = {
                      ...lastMessage.extra_data,
                      reasoning_steps: chunk.extra_data.reasoning_steps
                    }
                  }

                  if (chunk.extra_data?.references) {
                    lastMessage.extra_data = {
                      ...lastMessage.extra_data,
                      references: chunk.extra_data.references
                    }
                  }

                  lastMessage.created_at =
                    chunk.created_at ?? lastMessage.created_at
                  if (chunk.images) {
                    lastMessage.images = chunk.images
                  }
                  if (chunk.videos) {
                    lastMessage.videos = chunk.videos
                  }
                  if (chunk.audio) {
                    lastMessage.audio = chunk.audio
                  }
                } else if (
                  typeof chunk?.content !== 'string' &&
                  chunk.content !== null
                ) {
                  const jsonBlock = getJsonMarkdown(chunk?.content)

                  lastMessage.content += jsonBlock
                  lastContent = jsonBlock
                } else if (
                  chunk.response_audio?.transcript &&
                  typeof chunk.response_audio?.transcript === 'string'
                ) {
                  const transcript = chunk.response_audio.transcript
                  lastMessage.response_audio = {
                    ...lastMessage.response_audio,
                    transcript:
                      (lastMessage.response_audio?.transcript || '') + transcript
                  }
                }
                return newMessages
              })
            } else if (chunk.event === RunEvent.ReasoningCompleted) {
              setMessages((prevMessages) => {
                const newMessages = [...prevMessages]
                const lastMessage = newMessages[newMessages.length - 1]
                if (lastMessage && lastMessage.role === 'agent') {
                  if (chunk.extra_data?.reasoning_steps) {
                    lastMessage.extra_data = {
                      ...lastMessage.extra_data,
                      reasoning_steps: chunk.extra_data.reasoning_steps
                    }
                  }
                }
                return newMessages
              })
            } else if (chunk.event === RunEvent.RunError) {
              updateMessagesWithErrorState()
              const errorContent = chunk.content as string
              setStreamingErrorMessage(errorContent)
              if (hasStorage && newSessionId) {
                setSessionsData(
                  (prevSessionsData) =>
                    prevSessionsData?.filter(
                      (session) => session.id !== newSessionId
                    ) ?? null
                )
              }
            } else if (chunk.event === RunEvent.RunCompleted) {
              setMessages((prevMessages) => {
                const newMessages = prevMessages.map((message, index) => {
                  if (
                    index === prevMessages.length - 1 &&
                    message.role === 'agent'
                  ) {
                    // With new API, content is already accumulated from RunResponseContent events
                    // Just update final metadata and ensure content is properly set
                    const finalContent = chunk.content && typeof chunk.content === 'string'
                      ? chunk.content
                      : message.content;

                    return {
                      ...message,
                      content: finalContent,
                      images: chunk.images ?? message.images,
                      videos: chunk.videos ?? message.videos,
                      response_audio: chunk.response_audio ?? message.response_audio,
                      created_at: chunk.created_at ?? message.created_at,
                      extra_data: {
                        reasoning_steps:
                          chunk.extra_data?.reasoning_steps ??
                          message.extra_data?.reasoning_steps,
                        references:
                          chunk.extra_data?.references ??
                          message.extra_data?.references
                      }
                    }
                  }
                  return message
                })
                return newMessages
              })
            }
          },
          onError: (error) => {
            updateMessagesWithErrorState()
            setStreamingErrorMessage(error.message)
            if (hasStorage && newSessionId) {
              setSessionsData(
                (prevSessionsData) =>
                  prevSessionsData?.filter(
                    (session) => session.id !== newSessionId
                  ) ?? null
              )
            }
          },
          onComplete: () => {}
        })
      } catch (error) {
        updateMessagesWithErrorState()
        setStreamingErrorMessage(
          error instanceof Error ? error.message : String(error)
        )
        if (hasStorage && newSessionId) {
          setSessionsData(
            (prevSessionsData) =>
              prevSessionsData?.filter(
                (session) => session.id !== newSessionId
              ) ?? null
          )
        }
      } finally {
        focusChatInput()
        setIsStreaming(false)
      }
    },
    [
      setMessages,
      addMessage,
      updateMessagesWithErrorState,
      selectedEndpoint,
      userId,
      streamResponse,
      agentId,
      setStreamingErrorMessage,
      setIsStreaming,
      focusChatInput,
      setSessionsData,
      sessionId,
      setSessionId,
      hasStorage
    ]
  )

  return { handleStreamResponse }
}

export { useAIChatStreamHandler }
export default useAIChatStreamHandler
